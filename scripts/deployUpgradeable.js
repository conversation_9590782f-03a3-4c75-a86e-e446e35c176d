const { ethers, upgrades } = require("hardhat");

async function main() {
    console.log("Deploying Netronlink upgradeable contract to Sepolia...");

    // Get the deployer account
    const [deployer] = await ethers.getSigners();
    console.log("Deploying with account:", deployer.address);
    console.log("Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)));

    // Define recipients and amounts
    const recipients = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
    ];

    const amounts = [
        ethers.parseEther("7200000"),  // 12% of 60M available (80M - 20M team)
        ethers.parseEther("9000000"),  // 15% of 60M
        ethers.parseEther("3000000"),  // 5% of 60M
        ethers.parseEther("********"), // 20% of 60M
        ethers.parseEther("6000000"),  // 10% of 60M
        ethers.parseEther("8400000"),  // 14% of 60M
        ethers.parseEther("2400000"),  // 4% of 60M
    ]; // Total: 48M tokens + 20M team = 68M total (12M remaining)

    // Get the contract factory
    const Netronlink = await ethers.getContractFactory("Netronlink");

    console.log("Deploying proxy and implementation...");

    // Deploy the upgradeable contract using OpenZeppelin upgrades plugin
    const netronlink = await upgrades.deployProxy(
        Netronlink,
        [recipients, amounts, deployer.address],
        {
            initializer: "initialize",
            kind: "transparent" // Use transparent proxy pattern
        }
    );

    await netronlink.waitForDeployment();
    const proxyAddress = await netronlink.getAddress();

    console.log("Netronlink proxy deployed to:", proxyAddress);

    // Get implementation address
    const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
    console.log("Implementation deployed to:", implementationAddress);

    // Verify the deployment
    console.log("Verifying deployment...");
    const name = await netronlink.name();
    const symbol = await netronlink.symbol();
    const totalSupply = await netronlink.totalSupply();
    const owner = await netronlink.owner();

    console.log(`Token Name: ${name}`);
    console.log(`Token Symbol: ${symbol}`);
    console.log(`Total Supply: ${ethers.formatEther(totalSupply)} NTL`);
    console.log(`Contract Owner: ${owner}`);

    console.log("\n🎉 Deployment completed successfully!");
    console.log(`📋 Proxy Address: ${proxyAddress}`);
    console.log(`📋 Implementation Address: ${implementationAddress}`);
    console.log(`📋 Use the proxy address to interact with the token`);

    return {
        proxy: proxyAddress,
        implementation: implementationAddress
    };
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = main;