{"manifestVersion": "3.2", "proxies": [{"address": "0x2a24fE1F7c8Ab493BaF66c593ca664237F166AC7", "txHash": "0xfad20c7aee3e4e82b2319a3ad8530d448bf62585b700080737a5618ffc3003fb", "kind": "transparent"}], "impls": {"f9b4355a16f43822150fa9949363fc6dfc98fd91ada64c233df06617df17272d": {"address": "0x79D417A1497944899b3F30A965DE34aAe4379D73", "txHash": "0xc1bab47cb90b1fe77ed2e2d788a0a78ebc0e47fa7e6c80d69fc44b8b0263f220", "layout": {"solcVersion": "0.8.28", "storage": [{"label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:63", "retypedFrom": "bool"}, {"label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool", "contract": "Initializable", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:68"}, {"label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage", "contract": "ContextUpgradeable", "src": "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol:36"}, {"label": "_balances", "offset": 0, "slot": "51", "type": "t_mapping(t_address,t_uint256)", "contract": "ERC20Upgradeable", "src": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:40"}, {"label": "_allowances", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))", "contract": "ERC20Upgradeable", "src": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:42"}, {"label": "_totalSupply", "offset": 0, "slot": "53", "type": "t_uint256", "contract": "ERC20Upgradeable", "src": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:44"}, {"label": "_name", "offset": 0, "slot": "54", "type": "t_string_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:46"}, {"label": "_symbol", "offset": 0, "slot": "55", "type": "t_string_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:47"}, {"label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)45_storage", "contract": "ERC20Upgradeable", "src": "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol:376"}, {"label": "_owner", "offset": 0, "slot": "101", "type": "t_address", "contract": "OwnableUpgradeable", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:22"}, {"label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage", "contract": "OwnableUpgradeable", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:94"}, {"label": "vestingRecipient", "offset": 0, "slot": "151", "type": "t_address", "contract": "Netronlink", "src": "contracts/Netronlink.sol:15"}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "152", "type": "t_mapping(t_address,t_bool)", "contract": "Netronlink", "src": "contracts/Netronlink.sol:17"}, {"label": "teamVesting", "offset": 0, "slot": "153", "type": "t_struct(VestingSchedule)1442_storage", "contract": "Netronlink", "src": "contracts/Netronlink.sol:25"}, {"label": "__gap", "offset": 0, "slot": "156", "type": "t_array(t_uint256)45_storage", "contract": "Netronlink", "src": "contracts/Netronlink.sol:144"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)45_storage": {"label": "uint256[45]", "numberOfBytes": "1440"}, "t_array(t_uint256)49_storage": {"label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"label": "mapping(address => bool)", "numberOfBytes": "32"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32"}, "t_mapping(t_address,t_uint256)": {"label": "mapping(address => uint256)", "numberOfBytes": "32"}, "t_string_storage": {"label": "string", "numberOfBytes": "32"}, "t_struct(VestingSchedule)1442_storage": {"label": "struct Netronlink.VestingSchedule", "members": [{"label": "totalAllocated", "type": "t_uint256", "offset": 0, "slot": "0"}, {"label": "claimed", "type": "t_uint256", "offset": 0, "slot": "1"}, {"label": "startTime", "type": "t_uint64", "offset": 0, "slot": "2"}], "numberOfBytes": "96"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_uint8": {"label": "uint8", "numberOfBytes": "1"}}, "namespaces": {}}}}}