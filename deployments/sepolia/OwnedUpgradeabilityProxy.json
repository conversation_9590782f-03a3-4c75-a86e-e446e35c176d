{"address": "0xe6742C771298A19adaBd7cDe748087f355041276", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": false, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "ProxyOwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "impl", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maintenance", "outputs": [{"internalType": "bool", "name": "_maintenance", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "proxyOwner", "outputs": [{"internalType": "address", "name": "owner", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_maintenance", "type": "bool"}], "name": "setMaintenance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferProxyOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "transactionHash": "0xf6a34dee3de936043476b857b84ec114c88145f589a857ec5df24fa0dee2660f", "receipt": {"to": null, "from": "0xF96d2A2BDEdaFa702104e2386Af78FbECE04CEB3", "contractAddress": "0xe6742C771298A19adaBd7cDe748087f355041276", "transactionIndex": 118, "gasUsed": "794261", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xbde3dc53b368e96a0846a33104a40f7ed33385e5d0f29563d31ce47695d1a12a", "transactionHash": "0xf6a34dee3de936043476b857b84ec114c88145f589a857ec5df24fa0dee2660f", "logs": [], "blockNumber": 8675937, "cumulativeGasUsed": "15284499", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "3346ded06c9b932364bb4ae0c9f00ac9", "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"ProxyOwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"implementation\",\"type\":\"address\"}],\"name\":\"Upgraded\",\"type\":\"event\"},{\"stateMutability\":\"payable\",\"type\":\"fallback\"},{\"inputs\":[],\"name\":\"implementation\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"impl\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"maintenance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"_maintenance\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"proxyOwner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bool\",\"name\":\"_maintenance\",\"type\":\"bool\"}],\"name\":\"setMaintenance\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferProxyOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"}],\"name\":\"upgradeTo\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newImplementation\",\"type\":\"address\"},{\"internalType\":\"bytes\",\"name\":\"data\",\"type\":\"bytes\"}],\"name\":\"upgradeToAndCall\",\"outputs\":[],\"stateMutability\":\"payable\",\"type\":\"function\"},{\"stateMutability\":\"payable\",\"type\":\"receive\"}],\"devdoc\":{\"details\":\"This contract combines an upgradeability proxy with basic authorization control functionalities\",\"events\":{\"ProxyOwnershipTransferred(address,address)\":{\"details\":\"Event to show ownership has been transferred\",\"params\":{\"newOwner\":\"representing the address of the new owner\",\"previousOwner\":\"representing the address of the previous owner\"}},\"Upgraded(address)\":{\"details\":\"This event will be emitted every time the implementation gets upgraded\",\"params\":{\"implementation\":\"representing the address of the upgraded implementation\"}}},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"details\":\"the constructor sets the original owner of the contract to the sender account.\"},\"implementation()\":{\"details\":\"Tells the address of the current implementation\",\"returns\":{\"impl\":\"address of the current implementation\"}},\"maintenance()\":{\"details\":\"Tells if contract is on maintenance\",\"returns\":{\"_maintenance\":\"if contract is on maintenance\"}},\"proxyOwner()\":{\"details\":\"Tells the address of the owner\",\"returns\":{\"owner\":\"the address of the owner\"}},\"setMaintenance(bool)\":{\"details\":\"Sets if contract is on maintenance\"},\"transferProxyOwnership(address)\":{\"details\":\"Allows the current owner to transfer control of the contract to a newOwner.\",\"params\":{\"newOwner\":\"The address to transfer ownership to.\"}}},\"title\":\"OwnedUpgradeabilityProxy\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/OwnedUpgradeabilityProxy.sol\":\"OwnedUpgradeabilityProxy\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"contracts/OwnedUpgradeabilityProxy.sol\":{\"content\":\"// SPDX-License-Identifier: UNLICENSED\\n\\n\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @title OwnedUpgradeabilityProxy\\n * @dev This contract combines an upgradeability proxy with basic authorization control functionalities\\n */\\ncontract OwnedUpgradeabilityProxy {\\n    /**\\n     * @dev Event to show ownership has been transferred\\n     * @param previousOwner representing the address of the previous owner\\n     * @param newOwner representing the address of the new owner\\n     */\\n    event ProxyOwnershipTransferred(address previousOwner, address newOwner);\\n\\n    /**\\n     * @dev This event will be emitted every time the implementation gets upgraded\\n     * @param implementation representing the address of the upgraded implementation\\n     */\\n    event Upgraded(address indexed implementation);\\n\\n    // Storage position of the address of the maintenance boolean\\n    bytes32 private constant maintenancePosition = keccak256(\\\"com.NTL.proxy.maintenance\\\");\\n    // Storage position of the address of the current implementation\\n    bytes32 private constant implementationPosition = keccak256(\\\"com.NTL.proxy.implementation\\\");\\n    // Storage position of the owner of the contract\\n    bytes32 private constant proxyOwnerPosition = keccak256(\\\"com.NTL.proxy.owner\\\");\\n\\n    /**\\n     * @dev the constructor sets the original owner of the contract to the sender account.\\n     */\\n    constructor() {\\n        setUpgradeabilityOwner(msg.sender);\\n    }\\n\\n    /**\\n     * @dev Tells if contract is on maintenance\\n     * @return _maintenance if contract is on maintenance\\n     */\\n    function maintenance() public view returns (bool _maintenance) {\\n        bytes32 position = maintenancePosition;\\n        assembly {\\n            _maintenance := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Sets if contract is on maintenance\\n     */\\n    function setMaintenance(bool _maintenance) external onlyProxyOwner {\\n        bytes32 position = maintenancePosition;\\n        assembly {\\n            sstore(position, _maintenance)\\n        }\\n    }\\n\\n    /**\\n     * @dev Tells the address of the owner\\n     * @return owner the address of the owner\\n     */\\n    function proxyOwner() public view returns (address owner) {\\n        bytes32 position = proxyOwnerPosition;\\n        assembly {\\n            owner := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Sets the address of the owner\\n     */\\n    function setUpgradeabilityOwner(address newProxyOwner) internal {\\n        bytes32 position = proxyOwnerPosition;\\n        assembly {\\n            sstore(position, newProxyOwner)\\n        }\\n    }\\n\\n    /**\\n     * @dev Allows the current owner to transfer control of the contract to a newOwner.\\n     * @param newOwner The address to transfer ownership to.\\n     */\\n    function transferProxyOwnership(address newOwner) public onlyProxyOwner {\\n        require(newOwner != address(0), 'OwnedUpgradeabilityProxy: INVALID');\\n        emit ProxyOwnershipTransferred(proxyOwner(), newOwner);\\n        setUpgradeabilityOwner(newOwner);\\n    }\\n\\n    /*\\n     * @dev Allows the proxy owner to upgrade the current version of the proxy.\\n     * @param implementation representing the address of the new implementation to be set.\\n     */\\n    function upgradeTo(address newImplementation) public onlyProxyOwner {\\n        _upgradeTo(newImplementation);\\n    }\\n\\n    /*\\n     * @dev Allows the proxy owner to upgrade the current version of the proxy and call the new implementation\\n     * to initialize whatever is needed through a low level call.\\n     * @param implementation representing the address of the new implementation to be set.\\n     * @param data represents the msg.data to bet sent in the low level call. This parameter may include the function\\n     * signature of the implementation to be called with the needed payload\\n     */\\n    function upgradeToAndCall(address newImplementation, bytes memory data) payable public onlyProxyOwner {\\n        upgradeTo(newImplementation);\\n        (bool success, ) = address(this).call{ value: msg.value }(data);\\n        require(success, \\\"OwnedUpgradeabilityProxy: INVALID\\\");\\n    }\\n\\n    /**\\n     * @dev Fallback function allowing to perform a delegatecall to the given implementation.\\n     * This function will return whatever the implementation call returns\\n     */\\n    fallback() external payable {\\n        _fallback();\\n    }\\n\\n    receive () external payable {\\n        _fallback();\\n    }\\n\\n    /**\\n     * @dev Tells the address of the current implementation\\n     * @return impl address of the current implementation\\n     */\\n    function implementation() public view returns (address impl) {\\n        bytes32 position = implementationPosition;\\n        assembly {\\n            impl := sload(position)\\n        }\\n    }\\n\\n    /**\\n     * @dev Sets the address of the current implementation\\n     * @param newImplementation address representing the new implementation to be set\\n     */\\n    function setImplementation(address newImplementation) internal {\\n        bytes32 position = implementationPosition;\\n        assembly {\\n            sstore(position, newImplementation)\\n        }\\n    }\\n\\n    /**\\n     * @dev Upgrades the implementation address\\n     * @param newImplementation representing the address of the new implementation to be set\\n     */\\n    function _upgradeTo(address newImplementation) internal {\\n        address currentImplementation = implementation();\\n        require(currentImplementation != newImplementation, 'OwnedUpgradeabilityProxy: INVALID');\\n        setImplementation(newImplementation);\\n        emit Upgraded(newImplementation);\\n    }\\n\\n    function _fallback() internal {\\n        if (maintenance()) {\\n            require(msg.sender == proxyOwner(), 'OwnedUpgradeabilityProxy: FORBIDDEN');\\n        }\\n        address _impl = implementation();\\n        require(_impl != address(0), 'OwnedUpgradeabilityProxy: INVALID');\\n        assembly {\\n            let ptr := mload(0x40)\\n            calldatacopy(ptr, 0, calldatasize())\\n            let result := delegatecall(gas(), _impl, ptr, calldatasize(), 0, 0)\\n            let size := returndatasize()\\n            returndatacopy(ptr, 0, size)\\n\\n            switch result\\n            case 0 { revert(ptr, size) }\\n            default { return(ptr, size) }\\n        }\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyProxyOwner() {\\n        require(msg.sender == proxyOwner(), 'OwnedUpgradeabilityProxy: FORBIDDEN');\\n        _;\\n    }\\n}\",\"keccak256\":\"0xf20afa0f6c5b796b983e71b2af8d2b4961c5c862dbe67d6d83b43441d2f60f12\",\"license\":\"UNLICENSED\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"details": "This contract combines an upgradeability proxy with basic authorization control functionalities", "events": {"ProxyOwnershipTransferred(address,address)": {"details": "Event to show ownership has been transferred", "params": {"newOwner": "representing the address of the new owner", "previousOwner": "representing the address of the previous owner"}}, "Upgraded(address)": {"details": "This event will be emitted every time the implementation gets upgraded", "params": {"implementation": "representing the address of the upgraded implementation"}}}, "kind": "dev", "methods": {"constructor": {"details": "the constructor sets the original owner of the contract to the sender account."}, "implementation()": {"details": "Tells the address of the current implementation", "returns": {"impl": "address of the current implementation"}}, "maintenance()": {"details": "Tells if contract is on maintenance", "returns": {"_maintenance": "if contract is on maintenance"}}, "proxyOwner()": {"details": "Tells the address of the owner", "returns": {"owner": "the address of the owner"}}, "setMaintenance(bool)": {"details": "Sets if contract is on maintenance"}, "transferProxyOwnership(address)": {"details": "Allows the current owner to transfer control of the contract to a newOwner.", "params": {"newOwner": "The address to transfer ownership to."}}}, "title": "OwnedUpgradeabilityProxy", "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}, "storageLayout": {"storage": [], "types": null}}