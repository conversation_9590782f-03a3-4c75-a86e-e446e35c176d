{"address": "0xE2B13F52c846AA745bF3cE386a1B9754B4366709", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint8", "name": "version", "type": "uint8"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "presaleWallet", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "PresaleTokensClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TeamTokensClaimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "TokensBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "WhitelistRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "Whitelisted", "type": "event"}, {"inputs": [], "name": "CLIFF", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MONTHLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_MONTHS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimTeamTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getClaimableTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "recipients", "type": "address[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "address", "name": "teamAndFounder", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "teamVesting", "outputs": [{"internalType": "uint256", "name": "totalAllocated", "type": "uint256"}, {"internalType": "uint256", "name": "claimed", "type": "uint256"}, {"internalType": "uint64", "name": "startTime", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "vestingRecipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "transactionHash": "0x22149b67b869600a979584ec59ab684f08fa629f597027815f2f7dfde21016a3", "receipt": {"to": null, "from": "0xF96d2A2BDEdaFa702104e2386Af78FbECE04CEB3", "contractAddress": "0xE2B13F52c846AA745bF3cE386a1B9754B4366709", "transactionIndex": 343, "gasUsed": "2926494", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0x2708ce7616bc7381ddb233e3d350ec718f018a63faa61f8bb92f028d5620fc10", "transactionHash": "0x22149b67b869600a979584ec59ab684f08fa629f597027815f2f7dfde21016a3", "logs": [], "blockNumber": 8675936, "cumulativeGasUsed": "22979464", "status": 1, "byzantium": true}, "args": [], "numDeployments": 1, "solcInputHash": "3346ded06c9b932364bb4ae0c9f00ac9", "metadata": "{\"compiler\":{\"version\":\"0.8.28+commit.7893614a\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"uint8\",\"name\":\"version\",\"type\":\"uint8\"}],\"name\":\"Initialized\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"presaleWallet\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"PresaleTokensClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"recipient\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TeamTokensClaimed\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"TokensBurned\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"WhitelistRemoved\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Whitelisted\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"CLIFF\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_SUPPLY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MONTHLY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"TOTAL_MONTHS\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"addWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"claimTeamTokens\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"decimals\",\"outputs\":[{\"internalType\":\"uint8\",\"name\":\"\",\"type\":\"uint8\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"subtractedValue\",\"type\":\"uint256\"}],\"name\":\"decreaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getClaimableTokens\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"addedValue\",\"type\":\"uint256\"}],\"name\":\"increaseAllowance\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address[]\",\"name\":\"recipients\",\"type\":\"address[]\"},{\"internalType\":\"uint256[]\",\"name\":\"amounts\",\"type\":\"uint256[]\"},{\"internalType\":\"address\",\"name\":\"teamAndFounder\",\"type\":\"address\"}],\"name\":\"initialize\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"isWhitelisted\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"name\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"user\",\"type\":\"address\"}],\"name\":\"removeWhitelist\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"symbol\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"teamVesting\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalAllocated\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"claimed\",\"type\":\"uint256\"},{\"internalType\":\"uint64\",\"name\":\"startTime\",\"type\":\"uint64\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"version\",\"outputs\":[{\"internalType\":\"string\",\"name\":\"\",\"type\":\"string\"}],\"stateMutability\":\"pure\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"vestingRecipient\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Initialized(uint8)\":{\"details\":\"Triggered when the contract has been initialized or reinitialized.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"See {IERC20-allowance}.\"},\"approve(address,uint256)\":{\"details\":\"See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address.\"},\"balanceOf(address)\":{\"details\":\"See {IERC20-balanceOf}.\"},\"decimals()\":{\"details\":\"Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between Ether and Wei. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}.\"},\"decreaseAllowance(address,uint256)\":{\"details\":\"Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`.\"},\"increaseAllowance(address,uint256)\":{\"details\":\"Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address.\"},\"name()\":{\"details\":\"Returns the name of the token.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"symbol()\":{\"details\":\"Returns the symbol of the token, usually a shorter version of the name.\"},\"totalSupply()\":{\"details\":\"See {IERC20-totalSupply}.\"},\"transfer(address,uint256)\":{\"details\":\"See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/Netronlink.sol\":\"Netronlink\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\",\"useLiteralContent\":true},\"optimizer\":{\"enabled\":false,\"runs\":200},\"remappings\":[]},\"sources\":{\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (access/Ownable.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../utils/ContextUpgradeable.sol\\\";\\nimport \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Contract module which provides a basic access control mechanism, where\\n * there is an account (an owner) that can be granted exclusive access to\\n * specific functions.\\n *\\n * By default, the owner account will be the one that deploys the contract. This\\n * can later be changed with {transferOwnership}.\\n *\\n * This module is used through inheritance. It will make available the modifier\\n * `onlyOwner`, which can be applied to your functions to restrict their use to\\n * the owner.\\n */\\nabstract contract OwnableUpgradeable is Initializable, ContextUpgradeable {\\n    address private _owner;\\n\\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\\n\\n    /**\\n     * @dev Initializes the contract setting the deployer as the initial owner.\\n     */\\n    function __Ownable_init() internal onlyInitializing {\\n        __Ownable_init_unchained();\\n    }\\n\\n    function __Ownable_init_unchained() internal onlyInitializing {\\n        _transferOwnership(_msgSender());\\n    }\\n\\n    /**\\n     * @dev Throws if called by any account other than the owner.\\n     */\\n    modifier onlyOwner() {\\n        _checkOwner();\\n        _;\\n    }\\n\\n    /**\\n     * @dev Returns the address of the current owner.\\n     */\\n    function owner() public view virtual returns (address) {\\n        return _owner;\\n    }\\n\\n    /**\\n     * @dev Throws if the sender is not the owner.\\n     */\\n    function _checkOwner() internal view virtual {\\n        require(owner() == _msgSender(), \\\"Ownable: caller is not the owner\\\");\\n    }\\n\\n    /**\\n     * @dev Leaves the contract without owner. It will not be possible to call\\n     * `onlyOwner` functions. Can only be called by the current owner.\\n     *\\n     * NOTE: Renouncing ownership will leave the contract without an owner,\\n     * thereby disabling any functionality that is only available to the owner.\\n     */\\n    function renounceOwnership() public virtual onlyOwner {\\n        _transferOwnership(address(0));\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Can only be called by the current owner.\\n     */\\n    function transferOwnership(address newOwner) public virtual onlyOwner {\\n        require(newOwner != address(0), \\\"Ownable: new owner is the zero address\\\");\\n        _transferOwnership(newOwner);\\n    }\\n\\n    /**\\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\\n     * Internal function without access restriction.\\n     */\\n    function _transferOwnership(address newOwner) internal virtual {\\n        address oldOwner = _owner;\\n        _owner = newOwner;\\n        emit OwnershipTransferred(oldOwner, newOwner);\\n    }\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[49] private __gap;\\n}\\n\",\"keccak256\":\"0x4075622496acc77fd6d4de4cc30a8577a744d5c75afad33fdeacf1704d6eda98\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (proxy/utils/Initializable.sol)\\n\\npragma solidity ^0.8.2;\\n\\nimport \\\"../../utils/AddressUpgradeable.sol\\\";\\n\\n/**\\n * @dev This is a base contract to aid in writing upgradeable contracts, or any kind of contract that will be deployed\\n * behind a proxy. Since proxied contracts do not make use of a constructor, it's common to move constructor logic to an\\n * external initializer function, usually called `initialize`. It then becomes necessary to protect this initializer\\n * function so it can only be called once. The {initializer} modifier provided by this contract will have this effect.\\n *\\n * The initialization functions use a version number. Once a version number is used, it is consumed and cannot be\\n * reused. This mechanism prevents re-execution of each \\\"step\\\" but allows the creation of new initialization steps in\\n * case an upgrade adds a module that needs to be initialized.\\n *\\n * For example:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```solidity\\n * contract MyToken is ERC20Upgradeable {\\n *     function initialize() initializer public {\\n *         __ERC20_init(\\\"MyToken\\\", \\\"MTK\\\");\\n *     }\\n * }\\n *\\n * contract MyTokenV2 is MyToken, ERC20PermitUpgradeable {\\n *     function initializeV2() reinitializer(2) public {\\n *         __ERC20Permit_init(\\\"MyToken\\\");\\n *     }\\n * }\\n * ```\\n *\\n * TIP: To avoid leaving the proxy in an uninitialized state, the initializer function should be called as early as\\n * possible by providing the encoded function call as the `_data` argument to {ERC1967Proxy-constructor}.\\n *\\n * CAUTION: When used with inheritance, manual care must be taken to not invoke a parent initializer twice, or to ensure\\n * that all initializers are idempotent. This is not verified automatically as constructors are by Solidity.\\n *\\n * [CAUTION]\\n * ====\\n * Avoid leaving a contract uninitialized.\\n *\\n * An uninitialized contract can be taken over by an attacker. This applies to both a proxy and its implementation\\n * contract, which may impact the proxy. To prevent the implementation contract from being used, you should invoke\\n * the {_disableInitializers} function in the constructor to automatically lock it when it is deployed:\\n *\\n * [.hljs-theme-light.nopadding]\\n * ```\\n * /// @custom:oz-upgrades-unsafe-allow constructor\\n * constructor() {\\n *     _disableInitializers();\\n * }\\n * ```\\n * ====\\n */\\nabstract contract Initializable {\\n    /**\\n     * @dev Indicates that the contract has been initialized.\\n     * @custom:oz-retyped-from bool\\n     */\\n    uint8 private _initialized;\\n\\n    /**\\n     * @dev Indicates that the contract is in the process of being initialized.\\n     */\\n    bool private _initializing;\\n\\n    /**\\n     * @dev Triggered when the contract has been initialized or reinitialized.\\n     */\\n    event Initialized(uint8 version);\\n\\n    /**\\n     * @dev A modifier that defines a protected initializer function that can be invoked at most once. In its scope,\\n     * `onlyInitializing` functions can be used to initialize parent contracts.\\n     *\\n     * Similar to `reinitializer(1)`, except that functions marked with `initializer` can be nested in the context of a\\n     * constructor.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier initializer() {\\n        bool isTopLevelCall = !_initializing;\\n        require(\\n            (isTopLevelCall && _initialized < 1) || (!AddressUpgradeable.isContract(address(this)) && _initialized == 1),\\n            \\\"Initializable: contract is already initialized\\\"\\n        );\\n        _initialized = 1;\\n        if (isTopLevelCall) {\\n            _initializing = true;\\n        }\\n        _;\\n        if (isTopLevelCall) {\\n            _initializing = false;\\n            emit Initialized(1);\\n        }\\n    }\\n\\n    /**\\n     * @dev A modifier that defines a protected reinitializer function that can be invoked at most once, and only if the\\n     * contract hasn't been initialized to a greater version before. In its scope, `onlyInitializing` functions can be\\n     * used to initialize parent contracts.\\n     *\\n     * A reinitializer may be used after the original initialization step. This is essential to configure modules that\\n     * are added through upgrades and that require initialization.\\n     *\\n     * When `version` is 1, this modifier is similar to `initializer`, except that functions marked with `reinitializer`\\n     * cannot be nested. If one is invoked in the context of another, execution will revert.\\n     *\\n     * Note that versions can jump in increments greater than 1; this implies that if multiple reinitializers coexist in\\n     * a contract, executing them in the right order is up to the developer or operator.\\n     *\\n     * WARNING: setting the version to 255 will prevent any future reinitialization.\\n     *\\n     * Emits an {Initialized} event.\\n     */\\n    modifier reinitializer(uint8 version) {\\n        require(!_initializing && _initialized < version, \\\"Initializable: contract is already initialized\\\");\\n        _initialized = version;\\n        _initializing = true;\\n        _;\\n        _initializing = false;\\n        emit Initialized(version);\\n    }\\n\\n    /**\\n     * @dev Modifier to protect an initialization function so that it can only be invoked by functions with the\\n     * {initializer} and {reinitializer} modifiers, directly or indirectly.\\n     */\\n    modifier onlyInitializing() {\\n        require(_initializing, \\\"Initializable: contract is not initializing\\\");\\n        _;\\n    }\\n\\n    /**\\n     * @dev Locks the contract, preventing any future reinitialization. This cannot be part of an initializer call.\\n     * Calling this in the constructor of a contract will prevent that contract from being initialized or reinitialized\\n     * to any version. It is recommended to use this to lock implementation contracts that are designed to be called\\n     * through proxies.\\n     *\\n     * Emits an {Initialized} event the first time it is successfully executed.\\n     */\\n    function _disableInitializers() internal virtual {\\n        require(!_initializing, \\\"Initializable: contract is initializing\\\");\\n        if (_initialized != type(uint8).max) {\\n            _initialized = type(uint8).max;\\n            emit Initialized(type(uint8).max);\\n        }\\n    }\\n\\n    /**\\n     * @dev Returns the highest version that has been initialized. See {reinitializer}.\\n     */\\n    function _getInitializedVersion() internal view returns (uint8) {\\n        return _initialized;\\n    }\\n\\n    /**\\n     * @dev Returns `true` if the contract is currently initializing. See {onlyInitializing}.\\n     */\\n    function _isInitializing() internal view returns (bool) {\\n        return _initializing;\\n    }\\n}\\n\",\"keccak256\":\"0x89be10e757d242e9b18d5a32c9fbe2019f6d63052bbe46397a430a1d60d7f794\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC20/ERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"./IERC20Upgradeable.sol\\\";\\nimport \\\"./extensions/IERC20MetadataUpgradeable.sol\\\";\\nimport \\\"../../utils/ContextUpgradeable.sol\\\";\\nimport \\\"../../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Implementation of the {IERC20} interface.\\n *\\n * This implementation is agnostic to the way tokens are created. This means\\n * that a supply mechanism has to be added in a derived contract using {_mint}.\\n * For a generic mechanism see {ERC20PresetMinterPauser}.\\n *\\n * TIP: For a detailed writeup see our guide\\n * https://forum.openzeppelin.com/t/how-to-implement-erc20-supply-mechanisms/226[How\\n * to implement supply mechanisms].\\n *\\n * The default value of {decimals} is 18. To change this, you should override\\n * this function so it returns a different value.\\n *\\n * We have followed general OpenZeppelin Contracts guidelines: functions revert\\n * instead returning `false` on failure. This behavior is nonetheless\\n * conventional and does not conflict with the expectations of ERC20\\n * applications.\\n *\\n * Additionally, an {Approval} event is emitted on calls to {transferFrom}.\\n * This allows applications to reconstruct the allowance for all accounts just\\n * by listening to said events. Other implementations of the EIP may not emit\\n * these events, as it isn't required by the specification.\\n *\\n * Finally, the non-standard {decreaseAllowance} and {increaseAllowance}\\n * functions have been added to mitigate the well-known issues around setting\\n * allowances. See {IERC20-approve}.\\n */\\ncontract ERC20Upgradeable is Initializable, ContextUpgradeable, IERC20Upgradeable, IERC20MetadataUpgradeable {\\n    mapping(address => uint256) private _balances;\\n\\n    mapping(address => mapping(address => uint256)) private _allowances;\\n\\n    uint256 private _totalSupply;\\n\\n    string private _name;\\n    string private _symbol;\\n\\n    /**\\n     * @dev Sets the values for {name} and {symbol}.\\n     *\\n     * All two of these values are immutable: they can only be set once during\\n     * construction.\\n     */\\n    function __ERC20_init(string memory name_, string memory symbol_) internal onlyInitializing {\\n        __ERC20_init_unchained(name_, symbol_);\\n    }\\n\\n    function __ERC20_init_unchained(string memory name_, string memory symbol_) internal onlyInitializing {\\n        _name = name_;\\n        _symbol = symbol_;\\n    }\\n\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() public view virtual override returns (string memory) {\\n        return _name;\\n    }\\n\\n    /**\\n     * @dev Returns the symbol of the token, usually a shorter version of the\\n     * name.\\n     */\\n    function symbol() public view virtual override returns (string memory) {\\n        return _symbol;\\n    }\\n\\n    /**\\n     * @dev Returns the number of decimals used to get its user representation.\\n     * For example, if `decimals` equals `2`, a balance of `505` tokens should\\n     * be displayed to a user as `5.05` (`505 / 10 ** 2`).\\n     *\\n     * Tokens usually opt for a value of 18, imitating the relationship between\\n     * Ether and Wei. This is the default value returned by this function, unless\\n     * it's overridden.\\n     *\\n     * NOTE: This information is only used for _display_ purposes: it in\\n     * no way affects any of the arithmetic of the contract, including\\n     * {IERC20-balanceOf} and {IERC20-transfer}.\\n     */\\n    function decimals() public view virtual override returns (uint8) {\\n        return 18;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-totalSupply}.\\n     */\\n    function totalSupply() public view virtual override returns (uint256) {\\n        return _totalSupply;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-balanceOf}.\\n     */\\n    function balanceOf(address account) public view virtual override returns (uint256) {\\n        return _balances[account];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transfer}.\\n     *\\n     * Requirements:\\n     *\\n     * - `to` cannot be the zero address.\\n     * - the caller must have a balance of at least `amount`.\\n     */\\n    function transfer(address to, uint256 amount) public virtual override returns (bool) {\\n        address owner = _msgSender();\\n        _transfer(owner, to, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-allowance}.\\n     */\\n    function allowance(address owner, address spender) public view virtual override returns (uint256) {\\n        return _allowances[owner][spender];\\n    }\\n\\n    /**\\n     * @dev See {IERC20-approve}.\\n     *\\n     * NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on\\n     * `transferFrom`. This is semantically equivalent to an infinite approval.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function approve(address spender, uint256 amount) public virtual override returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev See {IERC20-transferFrom}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance. This is not\\n     * required by the EIP. See the note at the beginning of {ERC20}.\\n     *\\n     * NOTE: Does not update the allowance if the current allowance\\n     * is the maximum `uint256`.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` and `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `amount`.\\n     * - the caller must have allowance for ``from``'s tokens of at least\\n     * `amount`.\\n     */\\n    function transferFrom(address from, address to, uint256 amount) public virtual override returns (bool) {\\n        address spender = _msgSender();\\n        _spendAllowance(from, spender, amount);\\n        _transfer(from, to, amount);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Atomically increases the allowance granted to `spender` by the caller.\\n     *\\n     * This is an alternative to {approve} that can be used as a mitigation for\\n     * problems described in {IERC20-approve}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     */\\n    function increaseAllowance(address spender, uint256 addedValue) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        _approve(owner, spender, allowance(owner, spender) + addedValue);\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Atomically decreases the allowance granted to `spender` by the caller.\\n     *\\n     * This is an alternative to {approve} that can be used as a mitigation for\\n     * problems described in {IERC20-approve}.\\n     *\\n     * Emits an {Approval} event indicating the updated allowance.\\n     *\\n     * Requirements:\\n     *\\n     * - `spender` cannot be the zero address.\\n     * - `spender` must have allowance for the caller of at least\\n     * `subtractedValue`.\\n     */\\n    function decreaseAllowance(address spender, uint256 subtractedValue) public virtual returns (bool) {\\n        address owner = _msgSender();\\n        uint256 currentAllowance = allowance(owner, spender);\\n        require(currentAllowance >= subtractedValue, \\\"ERC20: decreased allowance below zero\\\");\\n        unchecked {\\n            _approve(owner, spender, currentAllowance - subtractedValue);\\n        }\\n\\n        return true;\\n    }\\n\\n    /**\\n     * @dev Moves `amount` of tokens from `from` to `to`.\\n     *\\n     * This internal function is equivalent to {transfer}, and can be used to\\n     * e.g. implement automatic token fees, slashing mechanisms, etc.\\n     *\\n     * Emits a {Transfer} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `from` cannot be the zero address.\\n     * - `to` cannot be the zero address.\\n     * - `from` must have a balance of at least `amount`.\\n     */\\n    function _transfer(address from, address to, uint256 amount) internal virtual {\\n        require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n        require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n        _beforeTokenTransfer(from, to, amount);\\n\\n        uint256 fromBalance = _balances[from];\\n        require(fromBalance >= amount, \\\"ERC20: transfer amount exceeds balance\\\");\\n        unchecked {\\n            _balances[from] = fromBalance - amount;\\n            // Overflow not possible: the sum of all balances is capped by totalSupply, and the sum is preserved by\\n            // decrementing then incrementing.\\n            _balances[to] += amount;\\n        }\\n\\n        emit Transfer(from, to, amount);\\n\\n        _afterTokenTransfer(from, to, amount);\\n    }\\n\\n    /** @dev Creates `amount` tokens and assigns them to `account`, increasing\\n     * the total supply.\\n     *\\n     * Emits a {Transfer} event with `from` set to the zero address.\\n     *\\n     * Requirements:\\n     *\\n     * - `account` cannot be the zero address.\\n     */\\n    function _mint(address account, uint256 amount) internal virtual {\\n        require(account != address(0), \\\"ERC20: mint to the zero address\\\");\\n\\n        _beforeTokenTransfer(address(0), account, amount);\\n\\n        _totalSupply += amount;\\n        unchecked {\\n            // Overflow not possible: balance + amount is at most totalSupply + amount, which is checked above.\\n            _balances[account] += amount;\\n        }\\n        emit Transfer(address(0), account, amount);\\n\\n        _afterTokenTransfer(address(0), account, amount);\\n    }\\n\\n    /**\\n     * @dev Destroys `amount` tokens from `account`, reducing the\\n     * total supply.\\n     *\\n     * Emits a {Transfer} event with `to` set to the zero address.\\n     *\\n     * Requirements:\\n     *\\n     * - `account` cannot be the zero address.\\n     * - `account` must have at least `amount` tokens.\\n     */\\n    function _burn(address account, uint256 amount) internal virtual {\\n        require(account != address(0), \\\"ERC20: burn from the zero address\\\");\\n\\n        _beforeTokenTransfer(account, address(0), amount);\\n\\n        uint256 accountBalance = _balances[account];\\n        require(accountBalance >= amount, \\\"ERC20: burn amount exceeds balance\\\");\\n        unchecked {\\n            _balances[account] = accountBalance - amount;\\n            // Overflow not possible: amount <= accountBalance <= totalSupply.\\n            _totalSupply -= amount;\\n        }\\n\\n        emit Transfer(account, address(0), amount);\\n\\n        _afterTokenTransfer(account, address(0), amount);\\n    }\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the `owner` s tokens.\\n     *\\n     * This internal function is equivalent to `approve`, and can be used to\\n     * e.g. set automatic allowances for certain subsystems, etc.\\n     *\\n     * Emits an {Approval} event.\\n     *\\n     * Requirements:\\n     *\\n     * - `owner` cannot be the zero address.\\n     * - `spender` cannot be the zero address.\\n     */\\n    function _approve(address owner, address spender, uint256 amount) internal virtual {\\n        require(owner != address(0), \\\"ERC20: approve from the zero address\\\");\\n        require(spender != address(0), \\\"ERC20: approve to the zero address\\\");\\n\\n        _allowances[owner][spender] = amount;\\n        emit Approval(owner, spender, amount);\\n    }\\n\\n    /**\\n     * @dev Updates `owner` s allowance for `spender` based on spent `amount`.\\n     *\\n     * Does not update the allowance amount in case of infinite allowance.\\n     * Revert if not enough allowance is available.\\n     *\\n     * Might emit an {Approval} event.\\n     */\\n    function _spendAllowance(address owner, address spender, uint256 amount) internal virtual {\\n        uint256 currentAllowance = allowance(owner, spender);\\n        if (currentAllowance != type(uint256).max) {\\n            require(currentAllowance >= amount, \\\"ERC20: insufficient allowance\\\");\\n            unchecked {\\n                _approve(owner, spender, currentAllowance - amount);\\n            }\\n        }\\n    }\\n\\n    /**\\n     * @dev Hook that is called before any transfer of tokens. This includes\\n     * minting and burning.\\n     *\\n     * Calling conditions:\\n     *\\n     * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n     * will be transferred to `to`.\\n     * - when `from` is zero, `amount` tokens will be minted for `to`.\\n     * - when `to` is zero, `amount` of ``from``'s tokens will be burned.\\n     * - `from` and `to` are never both zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _beforeTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n    /**\\n     * @dev Hook that is called after any transfer of tokens. This includes\\n     * minting and burning.\\n     *\\n     * Calling conditions:\\n     *\\n     * - when `from` and `to` are both non-zero, `amount` of ``from``'s tokens\\n     * has been transferred to `to`.\\n     * - when `from` is zero, `amount` tokens have been minted for `to`.\\n     * - when `to` is zero, `amount` of ``from``'s tokens have been burned.\\n     * - `from` and `to` are never both zero.\\n     *\\n     * To learn more about hooks, head to xref:ROOT:extending-contracts.adoc#using-hooks[Using Hooks].\\n     */\\n    function _afterTokenTransfer(address from, address to, uint256 amount) internal virtual {}\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[45] private __gap;\\n}\\n\",\"keccak256\":\"0xd14a627157b9a411d2410713e5dd3a377e9064bd5c194a90748bbf27ea625784\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/IERC20Upgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (token/ERC20/IERC20.sol)\\n\\npragma solidity ^0.8.0;\\n\\n/**\\n * @dev Interface of the ERC20 standard as defined in the EIP.\\n */\\ninterface IERC20Upgradeable {\\n    /**\\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\\n     * another (`to`).\\n     *\\n     * Note that `value` may be zero.\\n     */\\n    event Transfer(address indexed from, address indexed to, uint256 value);\\n\\n    /**\\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\\n     * a call to {approve}. `value` is the new allowance.\\n     */\\n    event Approval(address indexed owner, address indexed spender, uint256 value);\\n\\n    /**\\n     * @dev Returns the amount of tokens in existence.\\n     */\\n    function totalSupply() external view returns (uint256);\\n\\n    /**\\n     * @dev Returns the amount of tokens owned by `account`.\\n     */\\n    function balanceOf(address account) external view returns (uint256);\\n\\n    /**\\n     * @dev Moves `amount` tokens from the caller's account to `to`.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transfer(address to, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Returns the remaining number of tokens that `spender` will be\\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\\n     * zero by default.\\n     *\\n     * This value changes when {approve} or {transferFrom} are called.\\n     */\\n    function allowance(address owner, address spender) external view returns (uint256);\\n\\n    /**\\n     * @dev Sets `amount` as the allowance of `spender` over the caller's tokens.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\\n     * that someone may use both the old and the new allowance by unfortunate\\n     * transaction ordering. One possible solution to mitigate this race\\n     * condition is to first reduce the spender's allowance to 0 and set the\\n     * desired value afterwards:\\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-263524729\\n     *\\n     * Emits an {Approval} event.\\n     */\\n    function approve(address spender, uint256 amount) external returns (bool);\\n\\n    /**\\n     * @dev Moves `amount` tokens from `from` to `to` using the\\n     * allowance mechanism. `amount` is then deducted from the caller's\\n     * allowance.\\n     *\\n     * Returns a boolean value indicating whether the operation succeeded.\\n     *\\n     * Emits a {Transfer} event.\\n     */\\n    function transferFrom(address from, address to, uint256 amount) external returns (bool);\\n}\\n\",\"keccak256\":\"0x0e1f0f5f62f67a881cd1a9597acbc0a5e4071f3c2c10449a183b922ae7272e3f\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/token/ERC20/extensions/IERC20MetadataUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (token/ERC20/extensions/IERC20Metadata.sol)\\n\\npragma solidity ^0.8.0;\\n\\nimport \\\"../IERC20Upgradeable.sol\\\";\\n\\n/**\\n * @dev Interface for the optional metadata functions from the ERC20 standard.\\n *\\n * _Available since v4.1._\\n */\\ninterface IERC20MetadataUpgradeable is IERC20Upgradeable {\\n    /**\\n     * @dev Returns the name of the token.\\n     */\\n    function name() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the symbol of the token.\\n     */\\n    function symbol() external view returns (string memory);\\n\\n    /**\\n     * @dev Returns the decimals places of the token.\\n     */\\n    function decimals() external view returns (uint8);\\n}\\n\",\"keccak256\":\"0x605434219ebbe4653f703640f06969faa5a1d78f0bfef878e5ddbb1ca369ceeb\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/AddressUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts (last updated v4.9.0) (utils/Address.sol)\\n\\npragma solidity ^0.8.1;\\n\\n/**\\n * @dev Collection of functions related to the address type\\n */\\nlibrary AddressUpgradeable {\\n    /**\\n     * @dev Returns true if `account` is a contract.\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * It is unsafe to assume that an address for which this function returns\\n     * false is an externally-owned account (EOA) and not a contract.\\n     *\\n     * Among others, `isContract` will return false for the following\\n     * types of addresses:\\n     *\\n     *  - an externally-owned account\\n     *  - a contract in construction\\n     *  - an address where a contract will be created\\n     *  - an address where a contract lived, but was destroyed\\n     *\\n     * Furthermore, `isContract` will also return true if the target contract within\\n     * the same transaction is already scheduled for destruction by `SELFDESTRUCT`,\\n     * which only has an effect at the end of a transaction.\\n     * ====\\n     *\\n     * [IMPORTANT]\\n     * ====\\n     * You shouldn't rely on `isContract` to protect against flash loan attacks!\\n     *\\n     * Preventing calls from contracts is highly discouraged. It breaks composability, breaks support for smart wallets\\n     * like Gnosis Safe, and does not provide security since it can be circumvented by calling from a contract\\n     * constructor.\\n     * ====\\n     */\\n    function isContract(address account) internal view returns (bool) {\\n        // This method relies on extcodesize/address.code.length, which returns 0\\n        // for contracts in construction, since the code is only stored at the end\\n        // of the constructor execution.\\n\\n        return account.code.length > 0;\\n    }\\n\\n    /**\\n     * @dev Replacement for Solidity's `transfer`: sends `amount` wei to\\n     * `recipient`, forwarding all available gas and reverting on errors.\\n     *\\n     * https://eips.ethereum.org/EIPS/eip-1884[EIP1884] increases the gas cost\\n     * of certain opcodes, possibly making contracts go over the 2300 gas limit\\n     * imposed by `transfer`, making them unable to receive funds via\\n     * `transfer`. {sendValue} removes this limitation.\\n     *\\n     * https://consensys.net/diligence/blog/2019/09/stop-using-soliditys-transfer-now/[Learn more].\\n     *\\n     * IMPORTANT: because control is transferred to `recipient`, care must be\\n     * taken to not create reentrancy vulnerabilities. Consider using\\n     * {ReentrancyGuard} or the\\n     * https://solidity.readthedocs.io/en/v0.8.0/security-considerations.html#use-the-checks-effects-interactions-pattern[checks-effects-interactions pattern].\\n     */\\n    function sendValue(address payable recipient, uint256 amount) internal {\\n        require(address(this).balance >= amount, \\\"Address: insufficient balance\\\");\\n\\n        (bool success, ) = recipient.call{value: amount}(\\\"\\\");\\n        require(success, \\\"Address: unable to send value, recipient may have reverted\\\");\\n    }\\n\\n    /**\\n     * @dev Performs a Solidity function call using a low level `call`. A\\n     * plain `call` is an unsafe replacement for a function call: use this\\n     * function instead.\\n     *\\n     * If `target` reverts with a revert reason, it is bubbled up by this\\n     * function (like regular Solidity function calls).\\n     *\\n     * Returns the raw returned data. To convert to the expected return value,\\n     * use https://solidity.readthedocs.io/en/latest/units-and-global-variables.html?highlight=abi.decode#abi-encoding-and-decoding-functions[`abi.decode`].\\n     *\\n     * Requirements:\\n     *\\n     * - `target` must be a contract.\\n     * - calling `target` with `data` must not revert.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, \\\"Address: low-level call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`], but with\\n     * `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, 0, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but also transferring `value` wei to `target`.\\n     *\\n     * Requirements:\\n     *\\n     * - the calling contract must have an ETH balance of at least `value`.\\n     * - the called Solidity function must be `payable`.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(address target, bytes memory data, uint256 value) internal returns (bytes memory) {\\n        return functionCallWithValue(target, data, value, \\\"Address: low-level call with value failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCallWithValue-address-bytes-uint256-}[`functionCallWithValue`], but\\n     * with `errorMessage` as a fallback revert reason when `target` reverts.\\n     *\\n     * _Available since v3.1._\\n     */\\n    function functionCallWithValue(\\n        address target,\\n        bytes memory data,\\n        uint256 value,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        require(address(this).balance >= value, \\\"Address: insufficient balance for call\\\");\\n        (bool success, bytes memory returndata) = target.call{value: value}(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(address target, bytes memory data) internal view returns (bytes memory) {\\n        return functionStaticCall(target, data, \\\"Address: low-level static call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a static call.\\n     *\\n     * _Available since v3.3._\\n     */\\n    function functionStaticCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.staticcall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(address target, bytes memory data) internal returns (bytes memory) {\\n        return functionDelegateCall(target, data, \\\"Address: low-level delegate call failed\\\");\\n    }\\n\\n    /**\\n     * @dev Same as {xref-Address-functionCall-address-bytes-string-}[`functionCall`],\\n     * but performing a delegate call.\\n     *\\n     * _Available since v3.4._\\n     */\\n    function functionDelegateCall(\\n        address target,\\n        bytes memory data,\\n        string memory errorMessage\\n    ) internal returns (bytes memory) {\\n        (bool success, bytes memory returndata) = target.delegatecall(data);\\n        return verifyCallResultFromTarget(target, success, returndata, errorMessage);\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call to smart-contract was successful, and revert (either by bubbling\\n     * the revert reason or using the provided one) in case of unsuccessful call or if target was not a contract.\\n     *\\n     * _Available since v4.8._\\n     */\\n    function verifyCallResultFromTarget(\\n        address target,\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal view returns (bytes memory) {\\n        if (success) {\\n            if (returndata.length == 0) {\\n                // only check isContract if the call was successful and the return data is empty\\n                // otherwise we already know that it was a contract\\n                require(isContract(target), \\\"Address: call to non-contract\\\");\\n            }\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    /**\\n     * @dev Tool to verify that a low level call was successful, and revert if it wasn't, either by bubbling the\\n     * revert reason or using the provided one.\\n     *\\n     * _Available since v4.3._\\n     */\\n    function verifyCallResult(\\n        bool success,\\n        bytes memory returndata,\\n        string memory errorMessage\\n    ) internal pure returns (bytes memory) {\\n        if (success) {\\n            return returndata;\\n        } else {\\n            _revert(returndata, errorMessage);\\n        }\\n    }\\n\\n    function _revert(bytes memory returndata, string memory errorMessage) private pure {\\n        // Look for revert reason and bubble it up if present\\n        if (returndata.length > 0) {\\n            // The easiest way to bubble the revert reason is using memory via assembly\\n            /// @solidity memory-safe-assembly\\n            assembly {\\n                let returndata_size := mload(returndata)\\n                revert(add(32, returndata), returndata_size)\\n            }\\n        } else {\\n            revert(errorMessage);\\n        }\\n    }\\n}\\n\",\"keccak256\":\"0x9c80f545915582e63fe206c6ce27cbe85a86fc10b9cd2a0e8c9488fb7c2ee422\",\"license\":\"MIT\"},\"@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\n// OpenZeppelin Contracts v4.4.1 (utils/Context.sol)\\n\\npragma solidity ^0.8.0;\\nimport \\\"../proxy/utils/Initializable.sol\\\";\\n\\n/**\\n * @dev Provides information about the current execution context, including the\\n * sender of the transaction and its data. While these are generally available\\n * via msg.sender and msg.data, they should not be accessed in such a direct\\n * manner, since when dealing with meta-transactions the account sending and\\n * paying for execution may not be the actual sender (as far as an application\\n * is concerned).\\n *\\n * This contract is only required for intermediate, library-like contracts.\\n */\\nabstract contract ContextUpgradeable is Initializable {\\n    function __Context_init() internal onlyInitializing {\\n    }\\n\\n    function __Context_init_unchained() internal onlyInitializing {\\n    }\\n    function _msgSender() internal view virtual returns (address) {\\n        return msg.sender;\\n    }\\n\\n    function _msgData() internal view virtual returns (bytes calldata) {\\n        return msg.data;\\n    }\\n\\n    /**\\n     * @dev This empty reserved space is put in place to allow future versions to add new\\n     * variables without shifting down storage in the inheritance chain.\\n     * See https://docs.openzeppelin.com/contracts/4.x/upgradeable#storage_gaps\\n     */\\n    uint256[50] private __gap;\\n}\\n\",\"keccak256\":\"0x963ea7f0b48b032eef72fe3a7582edf78408d6f834115b9feadd673a4d5bd149\",\"license\":\"MIT\"},\"contracts/Netronlink.sol\":{\"content\":\"// SPDX-License-Identifier: MIT\\npragma solidity 0.8.28;\\n// final\\n\\nimport \\\"@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol\\\";\\nimport \\\"@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol\\\";\\n\\ncontract Netronlink is Initializable, ERC20Upgradeable, OwnableUpgradeable {\\n    uint256 public constant MAX_SUPPLY = 80000000 * 1e18;\\n    uint256 public constant CLIFF = 365 * 24 * 60 * 60; // 365 days in seconds\\n    uint256 public constant MONTHLY = 30 * 24 * 60 * 60; // 30 days in seconds\\n    uint256 public constant TOTAL_MONTHS = 24;\\n    uint256 internal constant teamAmount = ******** * 1e18;\\n    address public vestingRecipient;\\n\\n    mapping(address => bool) public isWhitelisted;\\n\\n    //Struct\\n    struct VestingSchedule {\\n        uint256 totalAllocated;\\n        uint256 claimed;\\n        uint64 startTime;\\n    }\\n    VestingSchedule public teamVesting;\\n\\n    //EVENTS--\\n    event Whitelisted(address indexed account);\\n    event WhitelistRemoved(address indexed account);\\n    event TeamTokensClaimed(address indexed recipient, uint256 amount);\\n    event TokensBurned(address indexed from, uint256 amount);\\n    event PresaleTokensClaimed(address presaleWallet, uint256 amount); \\n\\n    function initialize(\\n        address[] memory recipients,\\n        uint256[] memory amounts,\\n        address teamAndFounder\\n    ) public initializer {\\n        __ERC20_init(\\\"Netronlink Token\\\", \\\"NTL\\\");\\n        __Ownable_init();\\n\\n        require(recipients.length == amounts.length, \\\"Mismatched input arrays\\\");\\n\\n        uint256 totalToMint = 0;\\n        for (uint256 i = 0; i < amounts.length; i++) {\\n            totalToMint += amounts[i];\\n        }\\n\\n        require(totalToMint + teamAmount <= MAX_SUPPLY, \\\"Exceeds max supply\\\");\\n\\n        for (uint256 i = 0; i < recipients.length; i++) {\\n            _mint(recipients[i], amounts[i]);\\n        }\\n\\n        _mint(address(this), teamAmount);\\n\\n        require(teamAndFounder != address(0), \\\"teamAndFounder cannot be zero address\\\");\\n        teamVesting = VestingSchedule({\\n            totalAllocated: teamAmount,\\n            claimed: 0,\\n            startTime: uint64(block.timestamp)\\n        });\\n        vestingRecipient = teamAndFounder;\\n        addWhitelist(address(this));\\n    }\\n\\n    function _transfer(address from, address to, uint256 amount) internal override {\\n        require(from != address(0), \\\"ERC20: transfer from the zero address\\\");\\n        require(to != address(0), \\\"ERC20: transfer to the zero address\\\");\\n\\n        uint256 burnAmount = 0;\\n        uint256 sendAmount = amount;\\n\\n        if (!isWhitelisted[from] && !isWhitelisted[to]) {\\n            burnAmount = amount / 100; // 1%\\n            sendAmount = amount - burnAmount;\\n        }\\n\\n        // If there's a burn, handle it first by reducing the from balance\\n        if (burnAmount > 0) {\\n            _burn(from, burnAmount);\\n            emit TokensBurned(from, burnAmount); // burn log\\n        }\\n        \\n        // Call parent _transfer for the send amount\\n        super._transfer(from, to, sendAmount);\\n    }\\n\\n    function addWhitelist(address user) public onlyOwner {\\n        isWhitelisted[user] = true;\\n        emit Whitelisted(user);\\n    }\\n\\n    function removeWhitelist(address user) public onlyOwner {\\n        isWhitelisted[user] = false;\\n        emit WhitelistRemoved(user);\\n    }\\n\\n    function claimTeamTokens() external {\\n        require(msg.sender == vestingRecipient, \\\"Not authorized\\\");\\n\\n        uint256 claimable = _calculateClaimable();\\n        require(claimable > 0, \\\"Nothing to claim\\\");\\n\\n        teamVesting.claimed += claimable;\\n        _transfer(address(this), msg.sender, claimable);\\n\\n        emit TeamTokensClaimed(msg.sender, claimable);\\n    }\\n\\n    function _calculateClaimable() internal view returns (uint256 claimable) {\\n        VestingSchedule storage vest = teamVesting;\\n\\n        if (block.timestamp < vest.startTime + CLIFF) {\\n            return 0; // still in cliff\\n        }\\n\\n        uint256 monthsElapsed = (block.timestamp - vest.startTime - CLIFF) / MONTHLY;\\n        if (monthsElapsed > TOTAL_MONTHS) monthsElapsed = TOTAL_MONTHS;\\n\\n        uint256 totalVested = (vest.totalAllocated * monthsElapsed) / TOTAL_MONTHS;\\n        if (monthsElapsed == TOTAL_MONTHS) {\\n            totalVested = vest.totalAllocated;\\n        }\\n\\n        if (totalVested <= vest.claimed) {\\n            return 0;\\n        }\\n\\n        return totalVested - vest.claimed;\\n    }\\n\\n    function getClaimableTokens() external view returns (uint256) { \\n        require(msg.sender == vestingRecipient, \\\"Not authorized\\\");\\n        return _calculateClaimable();\\n    }\\n\\n    // Version function for upgrade tracking\\n    function version() external pure returns (string memory) {\\n        return \\\"1.0.0\\\";\\n    }\\n\\n    // Gap for future storage variables in upgrades\\n    uint256[45] private __gap;\\n}\\n\",\"keccak256\":\"0xde19b362ef0026895a2e3212fb40ba7657350b3dbfa29c7841e636e8116fa330\",\"license\":\"MIT\"}},\"version\":1}", "bytecode": "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", "deployedBytecode": "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", "devdoc": {"events": {"Approval(address,address,uint256)": {"details": "Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance."}, "Initialized(uint8)": {"details": "Triggered when the contract has been initialized or reinitialized."}, "Transfer(address,address,uint256)": {"details": "Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero."}}, "kind": "dev", "methods": {"allowance(address,address)": {"details": "See {IERC20-allowance}."}, "approve(address,uint256)": {"details": "See {IERC20-approve}. NOTE: If `amount` is the maximum `uint256`, the allowance is not updated on `transferFrom`. This is semantically equivalent to an infinite approval. Requirements: - `spender` cannot be the zero address."}, "balanceOf(address)": {"details": "See {IERC20-balanceOf}."}, "decimals()": {"details": "Returns the number of decimals used to get its user representation. For example, if `decimals` equals `2`, a balance of `505` tokens should be displayed to a user as `5.05` (`505 / 10 ** 2`). Tokens usually opt for a value of 18, imitating the relationship between <PERSON><PERSON> and <PERSON>. This is the default value returned by this function, unless it's overridden. NOTE: This information is only used for _display_ purposes: it in no way affects any of the arithmetic of the contract, including {IERC20-balanceOf} and {IERC20-transfer}."}, "decreaseAllowance(address,uint256)": {"details": "Atomically decreases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address. - `spender` must have allowance for the caller of at least `subtractedValue`."}, "increaseAllowance(address,uint256)": {"details": "Atomically increases the allowance granted to `spender` by the caller. This is an alternative to {approve} that can be used as a mitigation for problems described in {IERC20-approve}. Emits an {Approval} event indicating the updated allowance. Requirements: - `spender` cannot be the zero address."}, "name()": {"details": "Returns the name of the token."}, "owner()": {"details": "Returns the address of the current owner."}, "renounceOwnership()": {"details": "Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner."}, "symbol()": {"details": "Returns the symbol of the token, usually a shorter version of the name."}, "totalSupply()": {"details": "See {IERC20-totalSupply}."}, "transfer(address,uint256)": {"details": "See {IERC20-transfer}. Requirements: - `to` cannot be the zero address. - the caller must have a balance of at least `amount`."}, "transferFrom(address,address,uint256)": {"details": "See {IERC20-transferFrom}. Emits an {Approval} event indicating the updated allowance. This is not required by the EIP. See the note at the beginning of {ERC20}. NOTE: Does not update the allowance if the current allowance is the maximum `uint256`. Requirements: - `from` and `to` cannot be the zero address. - `from` must have a balance of at least `amount`. - the caller must have allowance for ``from``'s tokens of at least `amount`."}, "transferOwnership(address)": {"details": "Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}, "storageLayout": {"storage": [{"astId": 138, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_initialized", "offset": 0, "slot": "0", "type": "t_uint8"}, {"astId": 141, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_initializing", "offset": 1, "slot": "0", "type": "t_bool"}, {"astId": 1386, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "1", "type": "t_array(t_uint256)50_storage"}, {"astId": 319, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_balances", "offset": 0, "slot": "51", "type": "t_mapping(t_address,t_uint256)"}, {"astId": 325, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_allowances", "offset": 0, "slot": "52", "type": "t_mapping(t_address,t_mapping(t_address,t_uint256))"}, {"astId": 327, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_totalSupply", "offset": 0, "slot": "53", "type": "t_uint256"}, {"astId": 329, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_name", "offset": 0, "slot": "54", "type": "t_string_storage"}, {"astId": 331, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_symbol", "offset": 0, "slot": "55", "type": "t_string_storage"}, {"astId": 911, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "56", "type": "t_array(t_uint256)45_storage"}, {"astId": 10, "contract": "contracts/Netronlink.sol:Netronlink", "label": "_owner", "offset": 0, "slot": "101", "type": "t_address"}, {"astId": 130, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "102", "type": "t_array(t_uint256)49_storage"}, {"astId": 1431, "contract": "contracts/Netronlink.sol:Netronlink", "label": "vestingRecipient", "offset": 0, "slot": "151", "type": "t_address"}, {"astId": 1435, "contract": "contracts/Netronlink.sol:Netronlink", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offset": 0, "slot": "152", "type": "t_mapping(t_address,t_bool)"}, {"astId": 1445, "contract": "contracts/Netronlink.sol:Netronlink", "label": "teamVesting", "offset": 0, "slot": "153", "type": "t_struct(VestingSchedule)1442_storage"}, {"astId": 1869, "contract": "contracts/Netronlink.sol:Netronlink", "label": "__gap", "offset": 0, "slot": "156", "type": "t_array(t_uint256)45_storage"}], "types": {"t_address": {"encoding": "inplace", "label": "address", "numberOfBytes": "20"}, "t_array(t_uint256)45_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[45]", "numberOfBytes": "1440"}, "t_array(t_uint256)49_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[49]", "numberOfBytes": "1568"}, "t_array(t_uint256)50_storage": {"base": "t_uint256", "encoding": "inplace", "label": "uint256[50]", "numberOfBytes": "1600"}, "t_bool": {"encoding": "inplace", "label": "bool", "numberOfBytes": "1"}, "t_mapping(t_address,t_bool)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => bool)", "numberOfBytes": "32", "value": "t_bool"}, "t_mapping(t_address,t_mapping(t_address,t_uint256))": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => mapping(address => uint256))", "numberOfBytes": "32", "value": "t_mapping(t_address,t_uint256)"}, "t_mapping(t_address,t_uint256)": {"encoding": "mapping", "key": "t_address", "label": "mapping(address => uint256)", "numberOfBytes": "32", "value": "t_uint256"}, "t_string_storage": {"encoding": "bytes", "label": "string", "numberOfBytes": "32"}, "t_struct(VestingSchedule)1442_storage": {"encoding": "inplace", "label": "struct Netronlink.VestingSchedule", "members": [{"astId": 1437, "contract": "contracts/Netronlink.sol:Netronlink", "label": "totalAllocated", "offset": 0, "slot": "0", "type": "t_uint256"}, {"astId": 1439, "contract": "contracts/Netronlink.sol:Netronlink", "label": "claimed", "offset": 0, "slot": "1", "type": "t_uint256"}, {"astId": 1441, "contract": "contracts/Netronlink.sol:Netronlink", "label": "startTime", "offset": 0, "slot": "2", "type": "t_uint64"}], "numberOfBytes": "96"}, "t_uint256": {"encoding": "inplace", "label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"encoding": "inplace", "label": "uint64", "numberOfBytes": "8"}, "t_uint8": {"encoding": "inplace", "label": "uint8", "numberOfBytes": "1"}}}}